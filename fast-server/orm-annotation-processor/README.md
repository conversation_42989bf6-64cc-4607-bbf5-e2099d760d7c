# ORM 注解处理器

这个模块包含了一个注解处理器，用于自动生成类型化的表定义类，提供类型安全的查询构建功能。

## 功能特性

- **自动代码生成**: 基于 `@Table` 注解自动生成 `TypeTableInfo` 类
- **类型安全**: 为每个字段生成对应类型的 `TypedField` 方法
- **智能提示**: IDE 能根据字段类型提供精确的方法提示
- **编译时检查**: 在编译时发现类型错误和方法调用错误

## 工作原理

注解处理器会扫描所有标注了 `@Table` 注解的实体类，并为每个实体生成对应的类型化表定义类。

### 生成规则

1. **目标包**: 生成的类放在 `com.fast.core.basic.entity.data.type` 包下
2. **类名规则**: `Type` + 原实体类名（如 `TableInfo` → `TypeTableInfo`）
3. **字段映射**:
   - `String` 类型 → `TypedField.StringField`
   - `Boolean` 类型 → `TypedField.BooleanField`
   - `LocalDateTime` 类型 → `TypedField.DateTimeField`
   - 枚举类型 → `TypedField.StringField`

### 支持的字段

注解处理器会为以下字段生成方法：

#### BaseEntity 继承字段
- `id()` - 主键
- `createUserId()` - 创建人主键
- `creator()` - 创建人
- `createDate()` - 创建时间
- `modifyUserId()` - 修改人主键
- `modifier()` - 修改人
- `modifyDate()` - 修改时间
- `belongId()` - 归属ID

#### TableInfo 特有字段
- `name()` - 表名称
- `cnName()` - 中文名称
- `hasSubTables()` - 是否存在子表
- `hasParentTables()` - 是否存在父表
- `hasColumnSecurity()` - 是否存在列权限
- `hasRowSecurity()` - 是否存在行权限
- `hasSignRowSecurity()` - 是否存在单条行权限
- `hasAudit()` - 是否审计表
- `auditType()` - 审计类型
- `status()` - 表状态
- `description()` - 表说明

## 使用示例

### 基本用法

```java
// 获取表定义
TypeTableInfo table = TypeTableInfo.TABLE;

// 字符串字段操作
BaseTypeCondition nameCondition = table.name().like("%user%");
BaseTypeCondition statusCondition = table.status().eq("ACTIVE");

// 布尔字段操作
BaseTypeCondition hasSubTablesCondition = table.hasSubTables().eq(true);

// 日期时间字段操作
LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
BaseTypeCondition recentCondition = table.createDate().gt(lastMonth);

// 排序
Order nameAsc = table.name().asc();
Order createDateDesc = table.createDate().desc();
```

### 复杂查询

```java
TypeTableInfo table = TypeTableInfo.TABLE;

// 组合查询条件
BaseTypeCondition complexCondition = 
    table.name().like("%user%")
    .and(table.hasSubTables().eq(true))
    .and(table.createDate().gt(LocalDateTime.now().minusMonths(1)));

// 多重排序
List<Order> orders = Arrays.asList(
    table.createDate().desc(),
    table.name().asc()
);
```

## 类型安全的好处

1. **编译时检查**: 错误的字段名或方法调用会在编译时被发现
2. **IDE 智能提示**: 
   - 字符串字段提供 `like()`, `eq()`, `in()` 等方法
   - 数字字段提供 `gt()`, `lt()`, `between()` 等比较方法
   - 布尔字段只提供 `eq()`, `isNull()` 等基础方法
3. **重构友好**: 字段名变更会自动更新所有引用
4. **类型匹配**: 防止将错误类型的值传递给字段方法

## 生成的代码结构

```java
public class TypeTableInfo extends TypedTable<TableInfo> {
    public static final TypeTableInfo TABLE = new TypeTableInfo();
    
    public TypedField.StringField name() {
        SelectField selectField = SelectField.builder()
            .name("name")
            .build();
        return new TypedField.StringField(selectField);
    }
    
    // ... 其他字段方法
    
    @Override
    public Class<TableInfo> getEntityClass() {
        return TableInfo.class;
    }
}
```

## 配置和构建

注解处理器会在编译时自动运行，无需额外配置。确保项目依赖了必要的 ORM 模块：

```kotlin
dependencies {
    implementation(project(":fast-server:orm"))
    annotationProcessor(project(":fast-server:orm-annotation-processor"))
}
```

## 扩展

如果需要为其他实体类生成类型化表定义，只需确保实体类标注了 `@Table` 注解，注解处理器会自动处理。

对于新的字段类型，可以在 `generateFieldMethods()` 方法中添加相应的生成逻辑。
