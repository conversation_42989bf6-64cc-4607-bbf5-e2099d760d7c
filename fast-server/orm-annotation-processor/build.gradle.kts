plugins {
  java
  `java-library`
}


var persistenceVersion = "3.2.0"

dependencies {


  implementation(project(":fast-server:orm")) // 依赖自定义注解
  implementation("jakarta.persistence:jakarta.persistence-api:${persistenceVersion}")
  implementation("com.google.auto.service:auto-service:1.1.1") // 自动注册处理器
  implementation("com.squareup:javapoet:1.13.0") // 可选：简化代码生成（推荐）



  annotationProcessor("com.google.auto.service:auto-service:1.1.1")

  testImplementation(platform("org.junit:junit-bom:5.10.0"))
  testImplementation("org.junit.jupiter:junit-jupiter")
}

tasks.test {
  useJUnitPlatform()
}
