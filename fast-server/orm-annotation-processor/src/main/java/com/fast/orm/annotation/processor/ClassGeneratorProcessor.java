package com.fast.orm.annotation.processor;

import com.google.auto.service.AutoService;

import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.TypeElement;
import javax.tools.Diagnostic;
import javax.tools.JavaFileObject;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Set;

import jakarta.persistence.Table;

@AutoService(Processor.class) // 自动生成 META-INF 配置（需依赖 com.google.auto.service:auto-service）
public class ClassGeneratorProcessor extends AbstractProcessor {

  private Filer filer; // 用于生成新文件
  private Messager messager; // 日志输出

  @Override
  public synchronized void init(ProcessingEnvironment env) {
    super.init(env);
    filer = env.getFiler();
    messager = env.getMessager();
  }

  @Override
  public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment env) {
    for (TypeElement annotation : annotations) {
      // 遍历被 @GenerateClass 标记的类
      for (Element element : env.getElementsAnnotatedWith(annotation)) {
        if (element.getKind() == ElementKind.CLASS) {
          TypeElement classElement = (TypeElement) element;
          generateCode(classElement); // 生成新类
        }
      }
    }
    return true; // 声明已处理完毕
  }

  private void generateCode(TypeElement classElement) {
    String className = classElement.getSimpleName().toString();
    String packageName = processingEnv.getElementUtils().getPackageOf(classElement).toString();
    Table annot = classElement.getAnnotation(Table.class);

    // 生成 TypeTableInfo 类到指定包
    String targetPackage = "com.fast.core.basic.entity.data.type";
    String newClassName = "Type" + className;
    String fullClassName = targetPackage + "." + newClassName;

    try {
      JavaFileObject file = filer.createSourceFile(fullClassName);
      try (PrintWriter writer = new PrintWriter(file.openWriter())) {
        generateTypeTableInfoClass(writer, targetPackage, newClassName, className, packageName);
      }
    } catch (IOException e) {
      messager.printMessage(Diagnostic.Kind.ERROR, "Failed to generate class: " + e.getMessage());
    }
  }

  private void generateTypeTableInfoClass(PrintWriter writer, String targetPackage, String newClassName,
                                         String originalClassName, String originalPackage) {
    // 包声明
    writer.println("package " + targetPackage + ";");
    writer.println();

    // 导入语句
    writer.println("import " + originalPackage + "." + originalClassName + ";");
    writer.println("import com.fast.orm.curd.field.SelectField;");
    writer.println("import com.fast.orm.client.type.TypedField;");
    writer.println("import com.fast.orm.client.type.TypedTable;");
    writer.println();

    // 类声明
    writer.println("/**");
    writer.println(" * 自动生成的 " + originalClassName + " 类型化表定义");
    writer.println(" * 提供类型安全的字段访问方法");
    writer.println(" */");
    writer.println("public class " + newClassName + " extends TypedTable<" + originalClassName + "> {");
    writer.println();

    // 静态常量
    writer.println("    public static final " + newClassName + " TABLE = new " + newClassName + "();");
    writer.println();

    // 生成字段方法
    generateFieldMethods(writer);

    // getEntityClass 方法
    writer.println("    @Override");
    writer.println("    public Class<" + originalClassName + "> getEntityClass() {");
    writer.println("        return " + originalClassName + ".class;");
    writer.println("    }");
    writer.println("}");
  }

  private void generateFieldMethods(PrintWriter writer) {
    // BaseEntity 继承的字段
    generateStringField(writer, "id", "主键");
    generateStringField(writer, "createUserId", "创建人主键");
    generateStringField(writer, "creator", "创建人");
    generateDateTimeField(writer, "createDate", "创建时间");
    generateStringField(writer, "modifyUserId", "修改人主键");
    generateStringField(writer, "modifier", "修改人");
    generateDateTimeField(writer, "modifyDate", "修改时间");
    generateStringField(writer, "belongId", "归属id");

    // TableInfo 自身的字段
    generateStringField(writer, "name", "表名称");
    generateStringField(writer, "cnName", "中文名称");
    generateBooleanField(writer, "hasSubTables", "是否存在子表");
    generateBooleanField(writer, "hasParentTables", "是否存在父表");
    generateBooleanField(writer, "hasColumnSecurity", "是否存在列权限");
    generateBooleanField(writer, "hasRowSecurity", "是否存在行权限");
    generateBooleanField(writer, "hasSignRowSecurity", "是否存在单条行权限");
    generateBooleanField(writer, "hasAudit", "是否审计表");
    generateStringField(writer, "auditType", "审计类型");
    generateStringField(writer, "status", "表状态");
    generateStringField(writer, "description", "表说明");
  }

  private void generateStringField(PrintWriter writer, String fieldName, String comment) {
    writer.println("    /**");
    writer.println("     * " + comment);
    writer.println("     */");
    writer.println("    public TypedField.StringField " + fieldName + "() {");
    writer.println("        SelectField selectField = SelectField.builder()");
    writer.println("            .name(\"" + fieldName + "\")");
    writer.println("            .build();");
    writer.println("        return new TypedField.StringField(selectField);");
    writer.println("    }");
    writer.println();
  }

  private void generateBooleanField(PrintWriter writer, String fieldName, String comment) {
    writer.println("    /**");
    writer.println("     * " + comment);
    writer.println("     */");
    writer.println("    public TypedField.BooleanField " + fieldName + "() {");
    writer.println("        SelectField selectField = SelectField.builder()");
    writer.println("            .name(\"" + fieldName + "\")");
    writer.println("            .build();");
    writer.println("        return new TypedField.BooleanField(selectField);");
    writer.println("    }");
    writer.println();
  }

  private void generateDateTimeField(PrintWriter writer, String fieldName, String comment) {
    writer.println("    /**");
    writer.println("     * " + comment);
    writer.println("     */");
    writer.println("    public TypedField.DateTimeField " + fieldName + "() {");
    writer.println("        SelectField selectField = SelectField.builder()");
    writer.println("            .name(\"" + fieldName + "\")");
    writer.println("            .build();");
    writer.println("        return new TypedField.DateTimeField(selectField);");
    writer.println("    }");
    writer.println();
  }

  @Override
  public Set<String> getSupportedAnnotationTypes() {
    return Set.of(Table.class.getCanonicalName()); // 声明处理的注解
  }

  @Override
  public SourceVersion getSupportedSourceVersion() {
    return SourceVersion.latestSupported(); // 支持最新 Java 版本
  }
}
