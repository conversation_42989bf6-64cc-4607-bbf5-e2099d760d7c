package com.fast.orm.annotation.processor;

import com.google.auto.service.AutoService;

import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.TypeElement;
import javax.tools.Diagnostic;
import javax.tools.JavaFileObject;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Set;

import jakarta.persistence.Table;

@AutoService(Processor.class) // 自动生成 META-INF 配置（需依赖 com.google.auto.service:auto-service）
public class ClassGeneratorProcessor extends AbstractProcessor {

  private Filer filer; // 用于生成新文件
  private Messager messager; // 日志输出

  @Override
  public synchronized void init(ProcessingEnvironment env) {
    super.init(env);
    filer = env.getFiler();
    messager = env.getMessager();
  }

  @Override
  public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment env) {
    for (TypeElement annotation : annotations) {
      // 遍历被 @GenerateClass 标记的类
      for (Element element : env.getElementsAnnotatedWith(annotation)) {
        if (element.getKind() == ElementKind.CLASS) {
          TypeElement classElement = (TypeElement) element;
          generateCode(classElement); // 生成新类
        }
      }
    }
    return true; // 声明已处理完毕
  }

  private void generateCode(TypeElement classElement) {
    String className = classElement.getSimpleName().toString();
    String packageName = processingEnv.getElementUtils().getPackageOf(classElement).toString();
    Table annot = classElement.getAnnotation(Table.class);
    String newClassName = "Typed" + className;
    try {
      JavaFileObject file = filer.createSourceFile(packageName + "." + newClassName);
      try (PrintWriter writer = new PrintWriter(file.openWriter())) {
        writer.println("package " + packageName + ";");
        writer.println("public class " + newClassName + " {");
        writer.println("    public static void print() {");
        writer.println("        System.out.println(\"Generated by annotation processor!\");");
        writer.println("    }");
        writer.println("}");
      }
    } catch (IOException e) {
      messager.printMessage(Diagnostic.Kind.ERROR, "Failed to generate class: " + e.getMessage());
    }
    
  }

  @Override
  public Set<String> getSupportedAnnotationTypes() {
    return Set.of(Table.class.getCanonicalName()); // 声明处理的注解
  }

  @Override
  public SourceVersion getSupportedSourceVersion() {
    return SourceVersion.latestSupported(); // 支持最新 Java 版本
  }
}
