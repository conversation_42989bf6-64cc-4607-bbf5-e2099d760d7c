package com.fast.orm.curd.query;

import com.fast.core.data.ConstraintMeta;
import com.fast.core.data.FieldMeta;
import com.fast.core.data.TableMeta;
import com.fast.tools.utils.StringUtils;
import com.fast.orm.curd.AbstractCriteria;
import com.fast.orm.curd.FieldUtils;
import com.fast.orm.curd.JoinType;
import com.fast.orm.curd.TableFactory;
import com.fast.orm.curd.condition.*;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.page.Order;
import com.fast.orm.curd.page.Pageable;
import com.fast.orm.curd.temp.TempCondition;
import jakarta.validation.ValidationException;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.conf.ParamType;
import org.jooq.impl.DSL;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.fast.orm.curd.CriteriaUtils.*;


public class Query extends AbstractCriteria<Select<?>> {

  @Serial
  private static final long serialVersionUID = 6676367480150422243L;

  private final Boolean distinct;
  private final List<? extends SelectField> selectFields;
  private final BaseTypeCondition whereCondition;
  private final Boolean autoGroup;
  private final HavingCondition havingCondition;
  private final TempCondition tempCondition;
  private final Pageable pageable;

  private Select<?> countSelect;

  public Query(TableMeta tableMeta, Boolean distinct, List<? extends SelectField> selectFields, BaseTypeCondition whereCondition, Boolean autoGroup, HavingCondition havingCondition, TempCondition tempCondition) {
    this(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, Pageable.unPaged());
  }

  public Query(TableMeta tableMeta, Boolean distinct, List<? extends SelectField> selectFields,
               BaseTypeCondition whereCondition, Boolean autoGroup,
               HavingCondition havingCondition, TempCondition tempCondition, Pageable pageable) {
    super(tableMeta);
    this.distinct = distinct;
    this.selectFields = selectFields;
    this.whereCondition = whereCondition;
    this.autoGroup = autoGroup;
    this.havingCondition = havingCondition;
    this.tempCondition = tempCondition;
    this.pageable = pageable;
  }

  public void compile() {
    this.compile(null, null);
  }

  public void compile(BiFunction<Table<Record>, TableFactory, Condition> customCondition) {
    this.compile(null, customCondition);
  }

  public void compile(BiFunction<Table<Record>, TableFactory, List<Field<Object>>> customSelect, BiFunction<Table<Record>, TableFactory, Condition> customCondition) {
    boolean isPage = pageable != null && !pageable.getUnPaged();
    SelectQuery<Record> query = dsl.selectQuery();
    SelectQuery<Record> countQuery = dsl.selectQuery();
    // select * from table where conditon group by xx order by xx having xx;
    Table<Record> table = tableFactory.getMainTable();
    // 处理select 字段
    // 获取field 根据类型 返回field 加入 Select
    // 碰到对象字段，判断 join方式，子查询则不处理。join 则 添加新的关联表和关联方式。
    // 碰到_fn 字段，处理 查询 和 聚合函数 得到 field， join的表，先查询是否已经关联，取出关联 返回field
    List<Field<?>> fields = convertFields(table, tableMeta, selectFields, tableFactory);
    // 处理自定义字段
    if (customSelect != null) {
      fields.addAll(customSelect.apply(table, tableFactory));
    }
    // 添加字段
    query.addSelect(fields);
    if (isPage) {
      countQuery.addSelect(fields);
    }
    // 处理where字段
    // handle condition search join or add join ,no join add subQuery
    Condition where = DSL.noCondition();
    if (whereCondition != null) {
      where = convertCondition(table, tableMeta, whereCondition, tableFactory);
    }
    // 处理下有些情况下需要的自定义条件(如构建子查询，暂时无法获取正常的条件值)
    if (customCondition != null) {
      where = where.and(customCondition.apply(table, tableFactory));
    }
    query.addFrom(tableFactory.getReturnTable());
    query.addConditions(where);
    if (isPage) {
      countQuery.addFrom(tableFactory.getReturnTable());
      countQuery.addConditions(where);
    }
    // distinct 处理
    if (distinct) {
      query.setDistinct(true);
      if (isPage) {
        countQuery.setDistinct(true);
      }
    }
    // 处理group by
    // group by auto search field add group by or not auto group search group directive
    if (tableFactory.getGroup()) {
      query.addGroupBy(tableFactory.getGroupFields());
      if (isPage) {
        countQuery.addGroupBy(tableFactory.getGroupFields());
      }
      // 处理having
      // having to  condition
      if (havingCondition != null) {
        Condition having = convertHavingCondition(table, tableMeta, havingCondition, tableFactory);
        query.addHaving(having);
        if (isPage) {
          countQuery.addHaving(having);
        }
      }
    }
    // 处理order
    if (pageable != null) {
      List<Order> orders = pageable.getSort();
      List<? extends SortField<?>> sortFields = orders.stream().map(order -> convertOrder(table, tableMeta, order, tableFactory)).collect(Collectors.toList());
      query.addOrderBy(sortFields);
      if (isPage) {
        countQuery.addOrderBy(sortFields);
      }
    }
    // 处理temp
    if (tempCondition != null) {
      Condition temp = convertTempCondition(table, tableMeta, tempCondition, tableFactory);
      SelectQuery<Record> tempQuery = dsl.selectQuery();
      tempQuery.addFrom(query.asTable("_temp"));
      tempQuery.addConditions(temp);
      query = tempQuery;
      if (isPage) {
        countQuery = tempQuery;
      }
    }


    // 处理limit
    if (isPage) {
      // 添加count查询,存在分页才会limit
      this.countSelect = dsl.selectCount().from(countQuery.asTable("_countTemp"));
      // 添加limit
      Param<Number> offsetParam = tableFactory.generateParam("offsetNumber", (pageable.getPageNumber() - 1) * pageable.getPageSize());
//      DSL.param("{offsetNumber}", (pageable.getPageNumber() - 1) * pageable.getPageSize());
      Param<Number> nextParam = tableFactory.generateParam("nextNumber", pageable.getPageSize());
//      Param<Number> nextParam = DSL.param("{nextNumber}", pageable.getPageSize());
      query.addOffset(offsetParam);
      query.addLimit(nextParam);
    }
    this.criteria = query;
    this.compiled = true;
  }

  public String getCountSql() {
    boolean isPage = pageable != null && !pageable.getUnPaged();
    if (!isPage) {
      throw new RuntimeException("该查询不是一个分页查询");
    }
    return this.countSelect.getSQL(ParamType.NAMED);
  }

  private List<Field<?>> convertFields(Table<Record> currentTable, TableMeta currentTableMeta, List<? extends SelectField> selectFields, TableFactory tableFactory) {
    List<Field<?>> fields = new ArrayList<>();
    // 循环 select
    selectFields.forEach(selectField -> {
      // todo 后续验证下字段权限
      if (selectField.isFn()) {
        //出现聚合函数则，标记当前查询为group
        this.tableFactory.setGroup(true);
        AggregateField aggregateField = ((SelectFnField) selectField).getParam();
        Field<?> field = convertAggregateField(currentTable, currentTableMeta, aggregateField, tableFactory);
        if (StringUtils.isNotBlank(selectField.getAlias())) {
          field = field.as(selectField.getAlias());
        }
        fields.add(field);
      } else if (selectField.isRelation()) { // 判断是否关联字段
        FieldMeta fieldMeta = currentTableMeta.getField(selectField.getName());
        //判断是否是 _query 分页查询 先加上处理，如果实在没用后续删除. 其他_save,_del,_update 更不需要处理，报错就完事了。
        if (fieldMeta == null && selectField.getName().startsWith("_query")) {
          String fieldName = StringUtils.toLowerCaseFirstOne(selectField.getName().substring("_query".length()));
          fieldMeta = currentTableMeta.getField(fieldName);
        }
        if (fieldMeta == null) {
          throw new RuntimeException("没有找到字段：" + selectField.getName());
        }
        ConstraintMeta constraintMeta = currentTableMeta.getConstraint(fieldMeta.getName());
        if (constraintMeta == null) {
          throw new RuntimeException("没有找到关联字段：" + fieldMeta.getName());
        }
        // 判断子查询还是join,子查询不需要处理
        if (!JoinType.NONE.equals(constraintMeta.getJoinType())) {
          TableMeta joinTableMeta = currentTableMeta.getReactionTable(constraintMeta.getFieldName());
          Table<Record> joinTable = tableFactory.getTableByTableFieldName(constraintMeta.getFieldName());
          if (!tableFactory.existJoinTableByTableFieldName(constraintMeta.getFieldName())) {
            joinTable = tableFactory.generateJoinTable(joinTableMeta.getDbName(), constraintMeta.getFieldName());
            // 添加了条件，则覆盖一下returnTable
            Table<Record> currentReturnTable = joinTable(tableFactory.getReturnTable(), currentTable, joinTable, constraintMeta);
            tableFactory.setReturnTable(currentReturnTable);
          }
          // 如果是join，则先join下，然后再处理field
          List<Field<?>> childrenFields = convertFields(joinTable, joinTableMeta, selectField.getFields(), tableFactory);
          fields.addAll(childrenFields);
        } else {
          // 子查询则需要查询出来关联字段。
          List<? extends Field<?>> relationFields = constraintMeta.getConsColumns().stream().map(consColumnVo -> {
            Field<?> field = FieldUtils.generateField(currentTable, consColumnVo.getDbField());
            // 设置一个别名，方便获取关联属性值
            return field.as(selectField.getName() + "_" + consColumnVo.getDbField());
          }).toList();
          fields.addAll(relationFields);
        }
      } else {
        FieldMeta fieldMeta = currentTableMeta.getField(selectField.getName());
        Field<?> field = FieldUtils.generateField(currentTable, fieldMeta.getDbName());
        //非聚合函数则放入groupField列表后续备用
        if (this.autoGroup) {
          this.tableFactory.getGroupFields().add(field);
        } else {
          if (selectField.isGroup()) {
            this.tableFactory.getGroupFields().add(field);
          }
        }
        field = field.as(fieldMeta.getName());
        fields.add(field);
      }
    });
    return fields;
  }

  @SuppressWarnings("unchecked")
  private Field<?> convertAggregateField(Table<Record> currentTable, TableMeta currentTableMeta, AggregateField aggregateField, TableFactory tableFactory) {
    if (aggregateField.isEnd()) { // 最后一层则组装field
      FieldMeta fieldMeta = currentTableMeta.getField(aggregateField.getName());
      Field<?> tableField = FieldUtils.generateField(currentTable, fieldMeta.getDbName());
      Field<?> field = switch (aggregateField.getFn()) {
        case COUNT -> aggregateField.getDistinct() ? DSL.countDistinct(tableField) : DSL.count(tableField);
        case SUM ->
          aggregateField.getDistinct() ? DSL.sumDistinct((Field<? extends Number>) tableField) : DSL.sum((Field<? extends Number>) tableField);
        case AVG ->
          aggregateField.getDistinct() ? DSL.avgDistinct((Field<? extends Number>) tableField) : DSL.avg((Field<? extends Number>) tableField);
        case MAX -> aggregateField.getDistinct() ? DSL.maxDistinct(tableField) : DSL.max(tableField);
        case MIN -> aggregateField.getDistinct() ? DSL.minDistinct(tableField) : DSL.min(tableField);
        default -> throw new RuntimeException("不支持的聚合类型：" + aggregateField.getFn());
      };
      return field;
    } else { //向下关联
      AggregateField subAggregateField = aggregateField.getChild();
      ConstraintMeta constraintMeta = currentTableMeta.getConstraint(aggregateField.getName());
      if (constraintMeta == null) {
        throw new RuntimeException("没有找到关联字段：" + aggregateField.getName());
      }
      // 判断子查询还是join,子查询不需要处理
      if (!JoinType.NONE.equals(constraintMeta.getJoinType())) {
        TableMeta joinTableMeta = currentTableMeta.getReactionTable(constraintMeta.getFieldName());
        Table<Record> joinTable = tableFactory.getTableByTableFieldName(constraintMeta.getFieldName());
        if (!tableFactory.existJoinTableByTableFieldName(constraintMeta.getFieldName())) {
          joinTable = tableFactory.generateJoinTable(joinTableMeta.getDbName(), constraintMeta.getFieldName());
          // 添加了条件，则覆盖一下returnTable
          Table<Record> currentReturnTable = joinTable(tableFactory.getReturnTable(), currentTable, joinTable, constraintMeta);
          tableFactory.setReturnTable(currentReturnTable);
        }
        // 如果是join，则先join下，然后再处理field
        return convertAggregateField(joinTable, joinTableMeta, subAggregateField, tableFactory);
      } else {
        throw new RuntimeException("不支持的关联类型：" + constraintMeta.getJoinType());
      }
    }
  }


  private Condition convertHavingCondition(Table<Record> currentTable, TableMeta currentTableMeta, HavingCondition havingCondition, TableFactory tableFactory) {
    Condition condition = DSL.noCondition();
    if (havingCondition.getField() != null) {
      Field<Object> field = (Field<Object>) convertAggregateField(currentTable, currentTableMeta, havingCondition.getField(), tableFactory);
      List<Condition> andConditions = (havingCondition.getCondition().convert(field, new BaseCondition.ConditionField<Object>() {
        @Override
        public Field<Object> handleMax(Boolean max) {
          throw new ValidationException("object 类型字段暂不支持max");
        }

        @Override
        public Field<Object> handleField(String fieldPath) {
          return conditionField(fieldPath, Object.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<Object> handleParam(Object value) {
          return tableFactory.generateParam("having", value);
        }

        @Override
        public List<Param<Object>> handleParam(List<Object> value) {
          return value.stream().map(v -> tableFactory.generateParam("having", v)).collect(Collectors.toList());
        }
      }));
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (havingCondition.getAnd() != null) {
      List<Condition> andConditions = havingCondition.getAnd()
        .stream()
        .map(baseType -> convertHavingCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (havingCondition.getOr() != null) {
      List<Condition> orConditions = havingCondition.getOr()
        .stream()
        .map(baseType -> convertHavingCondition(currentTable, currentTableMeta, baseType, tableFactory))
        .collect(Collectors.toList());
      if (!orConditions.isEmpty()) {
        condition = condition.and(DSL.or(orConditions));
      }
    }
    if (havingCondition.getNot() != null) {
      condition = condition.andNot(
        convertHavingCondition(currentTable, currentTableMeta, havingCondition.getNot(), tableFactory));
    }
    return condition;
  }

  public SortField<?> convertOrder(Table<Record> currentTable, TableMeta currentTableMeta, Order order, TableFactory tableFactory) {
    if (order.isAlias()) {
      Field<?> field = DSL.field(order.getField());
      return handleDirection(field, order.getDirection());
    } else {
      if (order.isEnd()) {
        FieldMeta fieldMeta = currentTableMeta.getField(order.getField());
        Field<?> field = FieldUtils.generateField(currentTable, fieldMeta.getDbName());
        return handleDirection(field, order.getDirection());
      } else {
        TableMeta joinTableMeta = currentTableMeta.getReactionTable(order.getField());
        Table<Record> joinTable = tableFactory.getTableByTableFieldName(order.getField());
        return convertOrder(joinTable, joinTableMeta, order.getChild(), tableFactory);
      }
    }
  }

  public SortField<?> handleDirection(Field<?> field, Order.Direction direction) {
    if (Order.Direction.DESC.equals(direction)) {
      return field.desc();
    } else {
      return field.asc();
    }
  }

  private Condition convertTempCondition(Table<Record> currentTable, TableMeta currentTableMeta, TempCondition tempCondition, TableFactory tableFactory) {
    Condition condition = DSL.noCondition();
    if (tempCondition.getAlias() != null) {
      Field<Object> field = DSL.field(tempCondition.getAlias());
      List<Condition> andConditions = (tempCondition.getCondition().convert(field, new BaseCondition.ConditionField<Object>() {
        @Override
        public Field<Object> handleMax(Boolean max) {
          throw new ValidationException("object 类型字段暂不支持max");
        }

        @Override
        public Field<Object> handleField(String fieldPath) {
          return conditionField(fieldPath, Object.class, currentTable, currentTableMeta, tableFactory);
        }

        @Override
        public Param<Object> handleParam(Object value) {
          return tableFactory.generateParam("temp", value);
        }

        @Override
        public List<Param<Object>> handleParam(List<Object> value) {
          return value.stream().map(v -> tableFactory.generateParam("temp", v)).collect(Collectors.toList());
        }
      }));
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (tempCondition.getAnd() != null) {
      List<Condition> andConditions = tempCondition.getAnd()
        .stream()
        .map(it -> convertTempCondition(currentTable, currentTableMeta, it, tableFactory))
        .collect(Collectors.toList());
      if (!andConditions.isEmpty()) {
        condition = condition.and(DSL.and(andConditions));
      }
    }
    if (tempCondition.getOr() != null) {
      List<Condition> orConditions = tempCondition.getOr()
        .stream()
        .map(it -> convertTempCondition(currentTable, currentTableMeta, it, tableFactory))
        .collect(Collectors.toList());
      if (!orConditions.isEmpty()) {
        condition = condition.and(DSL.or(orConditions));
      }
    }
    if (tempCondition.getNot() != null) {
      condition = condition.andNot(
        convertTempCondition(currentTable, currentTableMeta, tempCondition.getNot(), tableFactory));
    }
    return condition;
  }

  public Query copy() {
    return new Query(this.tableMeta, this.distinct, this.selectFields, this.whereCondition, this.autoGroup, this.havingCondition, this.tempCondition, this.pageable);
  }

  public static Query create(TableMeta tableMeta, Boolean distinct, List<? extends SelectField> selectFields, BaseTypeCondition whereCondition, Boolean autoGroup, HavingCondition havingCondition, TempCondition tempCondition) {
    return new Query(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition);
  }

  public static Query create(TableMeta tableMeta, Boolean distinct, List<? extends SelectField> selectFields,
                             BaseTypeCondition whereCondition, Boolean autoGroup,
                             HavingCondition havingCondition, TempCondition tempCondition, List<Order> orders) {
    Pageable pageable = Pageable.unPaged();
    pageable.setSort(orders);
    return new Query(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, pageable);
  }

  public static Query create(TableMeta tableMeta, Boolean distinct, List<? extends SelectField> selectFields, BaseTypeCondition whereCondition, Boolean autoGroup, HavingCondition havingCondition, TempCondition tempCondition, Pageable pageable) {
    return new Query(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, pageable);
  }
}
