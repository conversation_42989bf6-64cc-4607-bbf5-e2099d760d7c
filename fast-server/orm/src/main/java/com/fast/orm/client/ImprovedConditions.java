package com.fast.orm.client;

import com.fast.orm.curd.condition.FieldCondition;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.temp.TempCondition;
import com.fast.orm.client.type.TypedField;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 改进的Having和Temp条件处理
 * 提供类型安全和完整的操作集合
 */
public class ImprovedConditions {

  /**
   * 改进的Having条件 - 支持完整的操作集合
   */
  public static class HavingField<T> {
    private final AggregateField field;

    private HavingField(AggregateField field) {
      this.field = field;
    }

    // 完整的比较操作
    public HavingCondition eq(T value) {
      return createCondition("eq", value);
    }

    public HavingCondition neq(T value) {
      return createCondition("neq", value);
    }

    public HavingCondition gt(T value) {
      return createCondition("gt", value);
    }

    public HavingCondition gte(T value) {
      return createCondition("gte", value);
    }

    public HavingCondition lt(T value) {
      return createCondition("lt", value);
    }

    public HavingCondition lte(T value) {
      return createCondition("lte", value);
    }

    public HavingCondition between(T start, T end) {
      return createCondition("between", List.of(start, end));
    }

    public HavingCondition in(List<T> values) {
      return createCondition("in", values);
    }

    public HavingCondition notIn(List<T> values) {
      return createCondition("nin", values);
    }

    public HavingCondition isNull() {
      return createCondition("isNull", true);
    }

    public HavingCondition isNotNull() {
      return createCondition("isNull", false);
    }

    private HavingCondition createCondition(String operation, Object value) {
      FieldCondition.FieldConditionBuilder builder = FieldCondition.builder();
      FieldCondition fieldCondition = switch (operation) {
        case "eq" -> builder.eq(value).build();
        case "neq" -> builder.neq(value).build();
        case "gt" -> builder.gt(value).build();
        case "gte" -> builder.gte(value).build();
        case "lt" -> builder.lt(value).build();
        case "lte" -> builder.lte(value).build();
        case "between" -> builder.between((List<Object>) value).build();
        case "in" -> builder.in((List<Object>) value).build();
        case "nin" -> builder.nin((List<Object>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation: " + operation);
      };
      return HavingCondition.builder().field(field).condition(fieldCondition).build();
    }
  }

  /**
   * 改进的Temp条件 - 支持类型安全和完整操作
   */
  public static class TempField<T> {
    private final String alias;
    private final boolean isStringType;

    private TempField(String alias, boolean isStringType) {
      this.alias = alias;
      this.isStringType = isStringType;
    }

    // 完整的比较操作
    public TempCondition eq(T value) {
      return createCondition("eq", value);
    }

    public TempCondition neq(T value) {
      return createCondition("neq", value);
    }

    public TempCondition gt(T value) {
      return createCondition("gt", value);
    }

    public TempCondition gte(T value) {
      return createCondition("gte", value);
    }

    public TempCondition lt(T value) {
      return createCondition("lt", value);
    }

    public TempCondition lte(T value) {
      return createCondition("lte", value);
    }

    public TempCondition between(T start, T end) {
      return createCondition("between", List.of(start, end));
    }

    public TempCondition in(List<T> values) {
      return createCondition("in", values);
    }

    public TempCondition notIn(List<T> values) {
      return createCondition("nin", values);
    }

    public TempCondition isNull() {
      return createCondition("isNull", true);
    }

    public TempCondition isNotNull() {
      return createCondition("isNull", false);
    }

    // 字符串特有操作
    public TempCondition like(String value) {
      if (!isStringType) throw new UnsupportedOperationException("like only supported for string temp fields");
      return createCondition("like", value);
    }

    public TempCondition nlike(String value) {
      if (!isStringType) throw new UnsupportedOperationException("nlike only supported for string temp fields");
      return createCondition("nlike", value);
    }

    private TempCondition createCondition(String operation, Object value) {
      FieldCondition.FieldConditionBuilder builder = FieldCondition.builder();
      FieldCondition fieldCondition = switch (operation) {
        case "eq" -> builder.eq(value).build();
        case "neq" -> builder.neq(value).build();
        case "gt" -> builder.gt(value).build();
        case "gte" -> builder.gte(value).build();
        case "lt" -> builder.lt(value).build();
        case "lte" -> builder.lte(value).build();
        case "between" -> builder.between((List<Object>) value).build();
        case "in" -> builder.in((List<Object>) value).build();
        case "nin" -> builder.nin((List<Object>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "like" -> builder.like((String) value).build();
        case "nlike" -> builder.nlike((String) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation: " + operation);
      };
      return TempCondition.builder().alias(alias).condition(fieldCondition).build();
    }
  }

  // ===== Having 静态工厂方法 =====

  // 通用方法（推荐用于COUNT等聚合函数）
  public static HavingField<Long> having(TypedField<?> field) {
    if (field.getField() instanceof SelectFnField selectFnField) {
      // 默认使用Long类型
      return new HavingField<>(selectFnField.getParam());
    } else {
      throw new IllegalArgumentException("Only SelectFnField is supported");
    }
  }

  // 类型安全的工厂方法
  public static HavingField<Integer> havingInt(TypedField<?> field) {
    if (field.getField() instanceof SelectFnField selectFnField) {
      // 默认使用Long类型
      return new HavingField<>(selectFnField.getParam());
    } else {
      throw new IllegalArgumentException("Only SelectFnField is supported");
    }
  }

  public static HavingField<Long> havingLong(TypedField<?> field) {
    if (field.getField() instanceof SelectFnField selectFnField) {
      // 默认使用Long类型
      return new HavingField<>(selectFnField.getParam());
    } else {
      throw new IllegalArgumentException("Only SelectFnField is supported");
    }
  }

  public static HavingField<BigDecimal> havingDecimal(TypedField<?> field) {
    if (field.getField() instanceof SelectFnField selectFnField) {
      // 默认使用Long类型
      return new HavingField<>(selectFnField.getParam());
    } else {
      throw new IllegalArgumentException("Only SelectFnField is supported");
    }
  }

  public static HavingField<String> havingString(TypedField<?> field) {
    if (field.getField() instanceof SelectFnField selectFnField) {
      // 默认使用Long类型
      return new HavingField<>(selectFnField.getParam());
    } else {
      throw new IllegalArgumentException("Only SelectFnField is supported");
    }
  }

  // ===== Temp 静态工厂方法 =====

  // 类型安全的工厂方法
  public static TempField<String> tempString(String alias) {
    return new TempField<>(alias, true);
  }

  public static TempField<Integer> tempInt(String alias) {
    return new TempField<>(alias, false);
  }

  public static TempField<Long> tempLong(String alias) {
    return new TempField<>(alias, false);
  }

  public static TempField<BigDecimal> tempDecimal(String alias) {
    return new TempField<>(alias, false);
  }

  public static TempField<LocalDate> tempDate(String alias) {
    return new TempField<>(alias, false);
  }

  public static TempField<LocalDateTime> tempDateTime(String alias) {
    return new TempField<>(alias, false);
  }

  public static TempField<Boolean> tempBoolean(String alias) {
    return new TempField<>(alias, false);
  }

  // 通用方法（向后兼容）
  public static TempField<Object> temp(String alias) {
    return new TempField<>(alias, false);
  }
}
