package com.fast.orm.client;

import com.fast.core.data.TableMeta;
import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.having.HavingCondition;
import com.fast.orm.curd.page.Order;
import com.fast.orm.curd.page.Pageable;
import com.fast.orm.curd.temp.TempCondition;
import com.fast.orm.client.type.TypedField;
import com.fast.orm.client.type.TypedTable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

public class QueryClient {

  protected TableMeta tableMeta;
  protected Boolean distinct = true;
  protected List<SelectField> selectFields;
  protected BaseTypeCondition whereCondition;
  protected Boolean autoGroup = true;
  protected HavingCondition havingCondition;
  protected TempCondition tempCondition;
  protected Pageable pageable;

  public QueryClient form(TypedTable<?> tableMeta) {
    this.tableMeta = tableMeta.getTableMeta();
    return this;
  }

  public QueryClient select(TypedField<?>... selectFields) {
    if (this.selectFields == null) {
      this.selectFields = new ArrayList<>();
    }
    List<SelectField> fields = Stream.of(selectFields).map(TypedField::getField).toList();
    this.selectFields.addAll(fields);
    return this;
  }

  public QueryClient distinct(Boolean distinct) {
    this.distinct = distinct;
    return this;
  }

  public QueryClient where(BaseTypeCondition whereCondition) {
    if (this.whereCondition == null) {
      this.whereCondition = whereCondition;
    } else {
      this.whereCondition.merge(whereCondition);
    }
    return this;
  }

  public QueryClient autoGroup(Boolean autoGroup) {
    this.autoGroup = autoGroup;
    return this;
  }

  public QueryClient having(HavingCondition havingCondition) {
    if (this.havingCondition == null) {
      this.havingCondition = havingCondition;
    } else {
      throw new RuntimeException("having条件只能设置一次");
    }
    return this;
  }

  public QueryClient orderBy(Order... orders) {
    if (this.pageable == null) {
      this.pageable = Pageable.unPaged();
    }
    this.pageable.setSort(List.of(orders));
    return this;
  }

  public QueryClient temp(TempCondition tempCondition) {
    if (this.tempCondition == null) {
      this.tempCondition = tempCondition;
    } else {
      throw new RuntimeException("temp条件只能设置一次");
    }
    return this;
  }

  public QueryClient limit(Integer pageNumber, Integer pageSize) {
    if (this.pageable == null) {
      this.pageable = Pageable.builder()
        .pageSize(pageSize)
        .pageNumber(pageNumber)
        .build();
    } else {
      this.pageable.setUnPaged(false);
      this.pageable.setPageNumber(pageNumber);
      this.pageable.setPageSize(pageSize);
    }
    return this;
  }


  public com.fast.orm.curd.query.Query build() {
    return com.fast.orm.curd.query.Query.create(tableMeta, distinct, selectFields, whereCondition, autoGroup, havingCondition, tempCondition, pageable);
  }

}
