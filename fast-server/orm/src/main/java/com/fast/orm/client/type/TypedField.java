package com.fast.orm.client.type;

import com.fast.orm.curd.AggregateType;
import com.fast.orm.curd.condition.*;
import com.fast.orm.curd.field.AggregateField;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.curd.field.SelectFnField;
import com.fast.orm.curd.page.Order;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 类型化字段实现
 * 通过不同的类实现不同的接口，确保IDE能正确提示可用方法
 */
public abstract class TypedField<T> {

  protected SelectField selectField;

  protected TypedField(SelectField selectField) {
    this.selectField = selectField;
  }


  public TypedField<T> distinct() {
    if (this.selectField instanceof SelectFnField selectFnField) {
      selectFnField.getParam().setDistinct(true);
    } else {
      throw new RuntimeException("distinct只能用于聚合函数。");
    }
    return this;
  }

  // 通用方法
  public BigDecimalField count() {
    AggregateField aggregateField = AggregateField.builder()
      .name(this.selectField.getName())
      .fn(AggregateType.COUNT)
      .distinct(false)
      .build();
    SelectFnField selectFnField = SelectFnField.fnBuilder()
      .param(aggregateField)
      .build();
    return new TypedField.BigDecimalField(selectFnField);
  }

  public BigDecimalField sum() {
    AggregateField aggregateField = AggregateField.builder()
      .name(this.selectField.getName())
      .fn(AggregateType.SUM)
      .distinct(false)
      .build();
    SelectFnField selectFnField = SelectFnField.fnBuilder()
      .param(aggregateField)
      .build();
    return new TypedField.BigDecimalField(selectFnField);
  }

  public BigDecimalField avg() {
    AggregateField aggregateField = AggregateField.builder()
      .name(this.selectField.getName())
      .fn(AggregateType.AVG)
      .distinct(false)
      .build();
    SelectFnField selectFnField = SelectFnField.fnBuilder()
      .param(aggregateField)
      .build();
    return new TypedField.BigDecimalField(selectFnField);
  }

  public TypedField<T> group() {
    SelectField.FieldDirective fieldDirective = SelectField.FieldDirective.builder()
      .name("group")
      .build();
    this.selectField.setDirective(Collections.singletonList(fieldDirective));
    return this;
  }

  public TypedField<T> as(String alias) {
    this.selectField.setAlias(alias);
    return this;
  }

  // 排序
  public Order asc() {
    return createAscOrder();
  }

  public Order desc() {
    return createDescOrder();
  }

  public SelectField getField() {
    return this.selectField;
  }

  // 抽象方法，由具体的字段类型实现
  protected abstract BaseCondition<?> createCondition(String operation, Object value);

  // 基础条件方法实现
  protected BaseTypeCondition createEqCondition(T value) {
    BaseCondition<?> condition = createCondition("eq", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createNeqCondition(T value) {
    BaseCondition<?> condition = createCondition("neq", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createIsNullCondition() {
    BaseCondition<?> condition = createCondition("isNull", true);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createIsNotNullCondition() {
    BaseCondition<?> condition = createCondition("isNull", false);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createInCondition(List<T> values) {
    BaseCondition<?> condition = createCondition("in", values);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createNotInCondition(List<T> values) {
    BaseCondition<?> condition = createCondition("nin", values);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createGtCondition(T value) {
    BaseCondition<?> condition = createCondition("gt", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createGteCondition(T value) {
    BaseCondition<?> condition = createCondition("gte", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createLtCondition(T value) {
    BaseCondition<?> condition = createCondition("lt", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createLteCondition(T value) {
    BaseCondition<?> condition = createCondition("lte", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createBetweenCondition(T start, T end) {
    BaseCondition<?> condition = createCondition("between", List.of(start, end));
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createLikeCondition(String value) {
    BaseCondition<?> condition = createCondition("like", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected BaseTypeCondition createNlikeCondition(String value) {
    BaseCondition<?> condition = createCondition("nlike", value);
    return BaseTypeCondition.builder().fieldCondition(this.selectField.getName(), condition).build();
  }

  protected Order createAscOrder() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.ASC).build();
  }

  protected Order createDescOrder() {
    return Order.builder().field(this.selectField.getName()).direction(Order.Direction.DESC).build();
  }

  /**
   * 字符串字段实现
   */
  public static class StringField extends TypedField<String> implements FieldOperations.StringField<String> {

    public StringField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      StringCondition.StringConditionBuilder builder = StringCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((String) value).build();
        case "neq" -> builder.neq((String) value).build();
        case "like" -> builder.like((String) value).build();
        case "nlike" -> builder.nlike((String) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in((List<String>) value).build();
        case "nin" -> builder.nin((List<String>) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for string: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(String value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(String value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<String> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<String> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition like(String value) {
      return createLikeCondition(value);
    }

    @Override
    public BaseTypeCondition nlike(String value) {
      return createNlikeCondition(value);
    }


    public StringField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.StringField(selectFnField);
    }

    public StringField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.StringField(selectFnField);
    }


  }

  /**
   * 整数字段实现
   */
  public static class IntegerField extends TypedField<Integer> implements FieldOperations.NumberField<Integer> {

    public IntegerField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      IntCondition.IntConditionBuilder builder = IntCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((Integer) value).build();
        case "neq" -> builder.neq((Integer) value).build();
        case "gt" -> builder.gt((Integer) value).build();
        case "gte" -> builder.gte((Integer) value).build();
        case "lt" -> builder.lt((Integer) value).build();
        case "lte" -> builder.lte((Integer) value).build();
        case "between" -> builder.between((List<Integer>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in((List<Integer>) value).build();
        case "nin" -> builder.nin((List<Integer>) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for integer: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(Integer value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(Integer value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<Integer> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<Integer> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition gt(Integer value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition gte(Integer value) {
      return createGteCondition(value);
    }

    @Override
    public BaseTypeCondition lt(Integer value) {
      return createLtCondition(value);
    }

    @Override
    public BaseTypeCondition lte(Integer value) {
      return createLteCondition(value);
    }

    @Override
    public BaseTypeCondition between(Integer start, Integer end) {
      return createBetweenCondition(start, end);
    }


    public IntegerField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.IntegerField(selectFnField);
    }

    public IntegerField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.IntegerField(selectFnField);
    }


  }

  /**
   * 大数字段实现
   */
  public static class LongField extends TypedField<Long> implements FieldOperations.NumberField<Long> {

    public LongField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      BigDecimalCondition.BigDecimalConditionBuilder builder = BigDecimalCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq(new BigDecimal((Long) value)).build();
        case "neq" -> builder.neq(new BigDecimal((Long) value)).build();
        case "gt" -> builder.gt(new BigDecimal((Long) value)).build();
        case "gte" -> builder.gte(new BigDecimal((Long) value)).build();
        case "lt" -> builder.lt(new BigDecimal((Long) value)).build();
        case "lte" -> builder.lte(new BigDecimal((Long) value)).build();
        case "between" -> builder.between(((List<Long>) value).stream().map(BigDecimal::new).toList()).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in(((List<Long>) value).stream().map(BigDecimal::new).toList()).build();
        case "nin" -> builder.nin(((List<Long>) value).stream().map(BigDecimal::new).toList()).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for Long: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(Long value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(Long value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<Long> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<Long> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition gt(Long value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition gte(Long value) {
      return createGteCondition(value);
    }

    @Override
    public BaseTypeCondition lt(Long value) {
      return createLtCondition(value);
    }

    @Override
    public BaseTypeCondition lte(Long value) {
      return createLteCondition(value);
    }

    @Override
    public BaseTypeCondition between(Long start, Long end) {
      return createBetweenCondition(start, end);
    }

    public LongField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.LongField(selectFnField);
    }

    public LongField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.LongField(selectFnField);
    }

  }


  /**
   * 大数字段实现
   */
  public static class BigDecimalField extends TypedField<BigDecimal> implements FieldOperations.NumberField<BigDecimal> {

    public BigDecimalField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      BigDecimalCondition.BigDecimalConditionBuilder builder = BigDecimalCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((BigDecimal) value).build();
        case "neq" -> builder.neq((BigDecimal) value).build();
        case "gt" -> builder.gt((BigDecimal) value).build();
        case "gte" -> builder.gte((BigDecimal) value).build();
        case "lt" -> builder.lt((BigDecimal) value).build();
        case "lte" -> builder.lte((BigDecimal) value).build();
        case "between" -> builder.between((List<BigDecimal>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in((List<BigDecimal>) value).build();
        case "nin" -> builder.nin((List<BigDecimal>) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for BigDecimal: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(BigDecimal value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(BigDecimal value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<BigDecimal> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<BigDecimal> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition gt(BigDecimal value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition gte(BigDecimal value) {
      return createGteCondition(value);
    }

    @Override
    public BaseTypeCondition lt(BigDecimal value) {
      return createLtCondition(value);
    }

    @Override
    public BaseTypeCondition lte(BigDecimal value) {
      return createLteCondition(value);
    }

    @Override
    public BaseTypeCondition between(BigDecimal start, BigDecimal end) {
      return createBetweenCondition(start, end);
    }

    public BigDecimalField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.BigDecimalField(selectFnField);
    }

    public BigDecimalField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.BigDecimalField(selectFnField);
    }
  }

  /**
   * 日期字段实现
   */
  public static class DateField extends TypedField<LocalDate> implements FieldOperations.DateField<LocalDate> {

    public DateField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      DateCondition.DateConditionBuilder builder = DateCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((LocalDate) value).build();
        case "neq" -> builder.neq((LocalDate) value).build();
        case "gt" -> builder.gt((LocalDate) value).build();
        case "gte" -> builder.gte((LocalDate) value).build();
        case "lt" -> builder.lt((LocalDate) value).build();
        case "lte" -> builder.lte((LocalDate) value).build();
        case "between" -> builder.between((List<LocalDate>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in((List<LocalDate>) value).build();
        case "nin" -> builder.nin((List<LocalDate>) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for LocalDate: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(LocalDate value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(LocalDate value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<LocalDate> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<LocalDate> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition gt(LocalDate value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition gte(LocalDate value) {
      return createGteCondition(value);
    }

    @Override
    public BaseTypeCondition lt(LocalDate value) {
      return createLtCondition(value);
    }

    @Override
    public BaseTypeCondition lte(LocalDate value) {
      return createLteCondition(value);
    }

    @Override
    public BaseTypeCondition between(LocalDate start, LocalDate end) {
      return createBetweenCondition(start, end);
    }

    public DateField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.DateField(selectFnField);
    }

    public DateField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.DateField(selectFnField);
    }
  }

  /**
   * 日期时间字段实现
   */
  public static class DateTimeField extends TypedField<LocalDateTime> implements FieldOperations.DateField<LocalDateTime> {

    public DateTimeField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      DateTimeCondition.DateTimeConditionBuilder builder = DateTimeCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((LocalDateTime) value).build();
        case "neq" -> builder.neq((LocalDateTime) value).build();
        case "gt" -> builder.gt((LocalDateTime) value).build();
        case "gte" -> builder.gte((LocalDateTime) value).build();
        case "lt" -> builder.lt((LocalDateTime) value).build();
        case "lte" -> builder.lte((LocalDateTime) value).build();
        case "between" -> builder.between((List<LocalDateTime>) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        case "in" -> builder.in((List<LocalDateTime>) value).build();
        case "nin" -> builder.nin((List<LocalDateTime>) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for LocalDateTime: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(LocalDateTime value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(LocalDateTime value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<LocalDateTime> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<LocalDateTime> values) {
      return createNotInCondition(values);
    }

    @Override
    public BaseTypeCondition gt(LocalDateTime value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition gte(LocalDateTime value) {
      return createGtCondition(value);
    }

    @Override
    public BaseTypeCondition lt(LocalDateTime value) {
      return createLtCondition(value);
    }

    @Override
    public BaseTypeCondition lte(LocalDateTime value) {
      return createLteCondition(value);
    }

    @Override
    public BaseTypeCondition between(LocalDateTime start, LocalDateTime end) {
      return createBetweenCondition(start, end);
    }

    public DateTimeField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.DateTimeField(selectFnField);
    }

    public DateTimeField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.DateTimeField(selectFnField);
    }

  }

  /**
   * 布尔字段实现
   */
  public static class BooleanField extends TypedField<Boolean> implements FieldOperations.BooleanField<Boolean> {

    public BooleanField(SelectField selectField) {
      super(selectField);
    }

    @Override
    protected BaseCondition<?> createCondition(String operation, Object value) {
      BooleanCondition.BooleanConditionBuilder builder = BooleanCondition.builder();
      return switch (operation) {
        case "eq" -> builder.eq((Boolean) value).build();
        case "neq" -> builder.neq((Boolean) value).build();
        case "isNull" -> builder.isNull((Boolean) value).build();
        default -> throw new UnsupportedOperationException("Unsupported operation for Boolean: " + operation);
      };
    }

    @Override
    public BaseTypeCondition eq(Boolean value) {
      return createEqCondition(value);
    }

    @Override
    public BaseTypeCondition neq(Boolean value) {
      return createNeqCondition(value);
    }

    @Override
    public BaseTypeCondition isNull() {
      return createIsNullCondition();
    }

    @Override
    public BaseTypeCondition isNotNull() {
      return createIsNotNullCondition();
    }

    @Override
    public BaseTypeCondition in(List<Boolean> values) {
      return createInCondition(values);
    }

    @Override
    public BaseTypeCondition notIn(List<Boolean> values) {
      return createNotInCondition(values);
    }


    public BooleanField min() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MIN)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.BooleanField(selectFnField);
    }

    public BooleanField max() {
      AggregateField aggregateField = AggregateField.builder()
        .name(this.selectField.getName())
        .fn(AggregateType.MAX)
        .distinct(false)
        .build();
      SelectFnField selectFnField = SelectFnField.fnBuilder()
        .param(aggregateField)
        .build();
      return new TypedField.BooleanField(selectFnField);
    }

  }


}
