package com.fast.orm.query;

import com.fast.orm.client.QueryClient;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.fast.orm.client.ImprovedConditions.having;
import static com.fast.orm.client.ImprovedConditions.temp;


public class QueryTest {

  @Test
  public void createQuery() {
    TypeTableInfo TABLE_INFO = TypeTableInfo.TABLE;
    QueryClient queryClient = new QueryClient();
    com.fast.orm.curd.query.Query queryBuild = queryClient.form(TABLE_INFO)
      .select(TABLE_INFO.id(), TABLE_INFO.name(), TABLE_INFO.cnName(), TABLE_INFO.id().count())
      .where(
        TABLE_INFO.id().eq("123")
          .and(TABLE_INFO.name().like("%xxx"))
      )
      .having(having(TABLE_INFO.id().count()).gt(1L))
      .temp(temp("test").gt(2))
      .orderBy(TABLE_INFO.id().desc())
      .limit(0, 10)
      .build();


    queryBuild.compile();
    String sql = queryBuild.getSql();
    Map<String, Object> params = queryBuild.getParams();
    System.out.println("sql: " + sql);
    // 打印params
    params.forEach((k, v) -> {
      System.out.println(k + ": " + v);
    });
  }

  @Test
  public void createQuery2() {
    TypeTableInfo TABLE_INFO = TypeTableInfo.TABLE;
    QueryClient queryClient = new QueryClient();
    com.fast.orm.curd.query.Query queryBuild = queryClient.form(TABLE_INFO)
      .select(TABLE_INFO.allFields())
      .where(
        TABLE_INFO.id().eq("123")
          .and(TABLE_INFO.name().like("%xxx"))
      )
      .orderBy(TABLE_INFO.id().desc())
      .limit(0, 10)
      .build();


    queryBuild.compile();
    String sql = queryBuild.getSql();
    Map<String, Object> params = queryBuild.getParams();
    System.out.println("sql: " + sql);
    // 打印params
    params.forEach((k, v) -> {
      System.out.println(k + ": " + v);
    });
  }


}
