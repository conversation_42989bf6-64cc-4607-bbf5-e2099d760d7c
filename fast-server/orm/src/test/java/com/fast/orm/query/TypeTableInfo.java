package com.fast.orm.query;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.client.type.TypedField;
import com.fast.orm.client.type.TypedTable;

public class TypeTableInfo extends TypedTable<TableInfo> {
  public static final TypeTableInfo TABLE = new TypeTableInfo();

  public TypedField.StringField id() {
    SelectField selectField = SelectField.builder()
      .name("id")
      .build();
    return new TypedField.StringField(selectField);
  }

  public TypedField.StringField name() {
    SelectField selectField = SelectField.builder()
      .name("name")
      .build();
    return new TypedField.StringField(selectField);
  }

  public TypedField.StringField cnName() {
    SelectField selectField = SelectField.builder()
      .name("cnName")
      .build();
    return new TypedField.StringField(selectField);
  }


  @Override
  public Class<TableInfo> getEntityClass() {
    return TableInfo.class;
  }
}
