package com.fast.core.basic.entity.data.type;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.orm.curd.field.SelectField;
import com.fast.orm.client.type.TypedField;
import com.fast.orm.client.type.TypedTable;

/**
 * 自动生成的 TableInfo 类型化表定义
 * 提供类型安全的字段访问方法
 */
public class TypeTableInfo extends TypedTable<TableInfo> {

    public static final TypeTableInfo TABLE = new TypeTableInfo();

    /**
     * 主键
     */
    public TypedField.StringField id() {
        SelectField selectField = SelectField.builder()
            .name("id")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 创建人主键
     */
    public TypedField.StringField createUserId() {
        SelectField selectField = SelectField.builder()
            .name("createUserId")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 创建人
     */
    public TypedField.StringField creator() {
        SelectField selectField = SelectField.builder()
            .name("creator")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 创建时间
     */
    public TypedField.DateTimeField createDate() {
        SelectField selectField = SelectField.builder()
            .name("createDate")
            .build();
        return new TypedField.DateTimeField(selectField);
    }

    /**
     * 修改人主键
     */
    public TypedField.StringField modifyUserId() {
        SelectField selectField = SelectField.builder()
            .name("modifyUserId")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 修改人
     */
    public TypedField.StringField modifier() {
        SelectField selectField = SelectField.builder()
            .name("modifier")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 修改时间
     */
    public TypedField.DateTimeField modifyDate() {
        SelectField selectField = SelectField.builder()
            .name("modifyDate")
            .build();
        return new TypedField.DateTimeField(selectField);
    }

    /**
     * 归属id
     */
    public TypedField.StringField belongId() {
        SelectField selectField = SelectField.builder()
            .name("belongId")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 表名称
     */
    public TypedField.StringField name() {
        SelectField selectField = SelectField.builder()
            .name("name")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 中文名称
     */
    public TypedField.StringField cnName() {
        SelectField selectField = SelectField.builder()
            .name("cnName")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 是否存在子表
     */
    public TypedField.BooleanField hasSubTables() {
        SelectField selectField = SelectField.builder()
            .name("hasSubTables")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 是否存在父表
     */
    public TypedField.BooleanField hasParentTables() {
        SelectField selectField = SelectField.builder()
            .name("hasParentTables")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 是否存在列权限
     */
    public TypedField.BooleanField hasColumnSecurity() {
        SelectField selectField = SelectField.builder()
            .name("hasColumnSecurity")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 是否存在行权限
     */
    public TypedField.BooleanField hasRowSecurity() {
        SelectField selectField = SelectField.builder()
            .name("hasRowSecurity")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 是否存在单条行权限
     */
    public TypedField.BooleanField hasSignRowSecurity() {
        SelectField selectField = SelectField.builder()
            .name("hasSignRowSecurity")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 是否审计表
     */
    public TypedField.BooleanField hasAudit() {
        SelectField selectField = SelectField.builder()
            .name("hasAudit")
            .build();
        return new TypedField.BooleanField(selectField);
    }

    /**
     * 审计类型
     */
    public TypedField.StringField auditType() {
        SelectField selectField = SelectField.builder()
            .name("auditType")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 表状态
     */
    public TypedField.StringField status() {
        SelectField selectField = SelectField.builder()
            .name("status")
            .build();
        return new TypedField.StringField(selectField);
    }

    /**
     * 表说明
     */
    public TypedField.StringField description() {
        SelectField selectField = SelectField.builder()
            .name("description")
            .build();
        return new TypedField.StringField(selectField);
    }

    @Override
    public Class<TableInfo> getEntityClass() {
        return TableInfo.class;
    }
}
