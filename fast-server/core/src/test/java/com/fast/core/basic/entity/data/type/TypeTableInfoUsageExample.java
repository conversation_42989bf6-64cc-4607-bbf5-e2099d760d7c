package com.fast.core.basic.entity.data.type;

import com.fast.orm.curd.condition.BaseTypeCondition;
import com.fast.orm.curd.page.Order;

import java.time.LocalDateTime;

/**
 * TypeTableInfo 使用示例
 * 展示如何使用自动生成的类型化表定义进行类型安全的查询构建
 */
public class TypeTableInfoUsageExample {

    public static void main(String[] args) {
        demonstrateStringFieldOperations();
        demonstrateBooleanFieldOperations();
        demonstrateDateTimeFieldOperations();
        demonstrateOrderingOperations();
        demonstrateComplexQueries();
    }

    /**
     * 演示字符串字段操作
     */
    private static void demonstrateStringFieldOperations() {
        System.out.println("=== 字符串字段操作演示 ===");
        
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 表名模糊查询
        BaseTypeCondition nameCondition = table.name().like("%user%");
        System.out.println("表名包含 'user': " + nameCondition);
        
        // 中文名称精确匹配
        BaseTypeCondition cnNameCondition = table.cnName().eq("用户表");
        System.out.println("中文名称等于 '用户表': " + cnNameCondition);
        
        // 状态不为空
        BaseTypeCondition statusCondition = table.status().isNotNull();
        System.out.println("状态不为空: " + statusCondition);
        
        // 描述为空
        BaseTypeCondition descCondition = table.description().isNull();
        System.out.println("描述为空: " + descCondition);
        
        // 创建人ID在指定列表中
        BaseTypeCondition creatorCondition = table.createUserId().in(
            java.util.Arrays.asList("user1", "user2", "user3")
        );
        System.out.println("创建人ID在指定列表中: " + creatorCondition);
    }

    /**
     * 演示布尔字段操作
     */
    private static void demonstrateBooleanFieldOperations() {
        System.out.println("\n=== 布尔字段操作演示 ===");
        
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 有子表
        BaseTypeCondition hasSubTablesCondition = table.hasSubTables().eq(true);
        System.out.println("有子表: " + hasSubTablesCondition);
        
        // 无父表
        BaseTypeCondition hasParentTablesCondition = table.hasParentTables().eq(false);
        System.out.println("无父表: " + hasParentTablesCondition);
        
        // 有列权限
        BaseTypeCondition hasColumnSecurityCondition = table.hasColumnSecurity().eq(true);
        System.out.println("有列权限: " + hasColumnSecurityCondition);
        
        // 审计状态不为空
        BaseTypeCondition hasAuditCondition = table.hasAudit().isNotNull();
        System.out.println("审计状态不为空: " + hasAuditCondition);
    }

    /**
     * 演示日期时间字段操作
     */
    private static void demonstrateDateTimeFieldOperations() {
        System.out.println("\n=== 日期时间字段操作演示 ===");
        
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastMonth = now.minusMonths(1);
        
        // 创建时间大于上个月
        BaseTypeCondition createDateCondition = table.createDate().gt(lastMonth);
        System.out.println("创建时间大于上个月: " + createDateCondition);
        
        // 修改时间在指定范围内
        BaseTypeCondition modifyDateCondition = table.modifyDate().between(lastMonth, now);
        System.out.println("修改时间在指定范围内: " + modifyDateCondition);
        
        // 创建时间不为空
        BaseTypeCondition createDateNotNullCondition = table.createDate().isNotNull();
        System.out.println("创建时间不为空: " + createDateNotNullCondition);
    }

    /**
     * 演示排序操作
     */
    private static void demonstrateOrderingOperations() {
        System.out.println("\n=== 排序操作演示 ===");
        
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 按表名升序
        Order nameAsc = table.name().asc();
        System.out.println("按表名升序: " + nameAsc);
        
        // 按创建时间降序
        Order createDateDesc = table.createDate().desc();
        System.out.println("按创建时间降序: " + createDateDesc);
        
        // 按修改时间升序
        Order modifyDateAsc = table.modifyDate().asc();
        System.out.println("按修改时间升序: " + modifyDateAsc);
    }

    /**
     * 演示复杂查询组合
     */
    private static void demonstrateComplexQueries() {
        System.out.println("\n=== 复杂查询组合演示 ===");
        
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 查询条件：表名包含'user'，有子表，创建时间在最近一个月内
        BaseTypeCondition nameCondition = table.name().like("%user%");
        BaseTypeCondition hasSubTablesCondition = table.hasSubTables().eq(true);
        BaseTypeCondition recentCondition = table.createDate().gt(LocalDateTime.now().minusMonths(1));
        
        System.out.println("复杂查询条件:");
        System.out.println("  - 表名包含 'user': " + nameCondition);
        System.out.println("  - 有子表: " + hasSubTablesCondition);
        System.out.println("  - 创建时间在最近一个月内: " + recentCondition);
        
        // 排序：先按创建时间降序，再按表名升序
        Order createDateDesc = table.createDate().desc();
        Order nameAsc = table.name().asc();
        
        System.out.println("排序条件:");
        System.out.println("  - 按创建时间降序: " + createDateDesc);
        System.out.println("  - 按表名升序: " + nameAsc);
        
        System.out.println("\n这样的查询构建提供了:");
        System.out.println("1. 类型安全 - IDE会检查字段类型和可用方法");
        System.out.println("2. 智能提示 - IDE会根据字段类型提示相应的操作方法");
        System.out.println("3. 编译时检查 - 错误的操作会在编译时被发现");
        System.out.println("4. 重构友好 - 字段名变更会自动更新所有引用");
    }
}
