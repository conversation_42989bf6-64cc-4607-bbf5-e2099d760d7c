package com.fast.core.basic.entity.data.type;

import com.fast.core.basic.entity.data.TableInfo;
import com.fast.orm.client.type.TypedField;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TypeTableInfo 测试类
 * 验证自动生成的类型化表定义是否正确工作
 */
public class TypeTableInfoTest {

    @Test
    public void testTableConstant() {
        // 测试静态常量
        assertNotNull(TypeTableInfo.TABLE);
        assertTrue(TypeTableInfo.TABLE instanceof TypeTableInfo);
    }

    @Test
    public void testGetEntityClass() {
        // 测试 getEntityClass 方法
        TypeTableInfo table = new TypeTableInfo();
        assertEquals(TableInfo.class, table.getEntityClass());
    }

    @Test
    public void testStringFields() {
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 测试字符串字段
        TypedField.StringField id = table.id();
        assertNotNull(id);
        assertEquals("id", id.getField().getName());
        
        TypedField.StringField name = table.name();
        assertNotNull(name);
        assertEquals("name", name.getField().getName());
        
        TypedField.StringField cnName = table.cnName();
        assertNotNull(cnName);
        assertEquals("cnName", cnName.getField().getName());
        
        TypedField.StringField status = table.status();
        assertNotNull(status);
        assertEquals("status", status.getField().getName());
        
        TypedField.StringField description = table.description();
        assertNotNull(description);
        assertEquals("description", description.getField().getName());
    }

    @Test
    public void testBooleanFields() {
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 测试布尔字段
        TypedField.BooleanField hasSubTables = table.hasSubTables();
        assertNotNull(hasSubTables);
        assertEquals("hasSubTables", hasSubTables.getField().getName());
        
        TypedField.BooleanField hasParentTables = table.hasParentTables();
        assertNotNull(hasParentTables);
        assertEquals("hasParentTables", hasParentTables.getField().getName());
        
        TypedField.BooleanField hasAudit = table.hasAudit();
        assertNotNull(hasAudit);
        assertEquals("hasAudit", hasAudit.getField().getName());
    }

    @Test
    public void testDateTimeFields() {
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 测试日期时间字段
        TypedField.DateTimeField createDate = table.createDate();
        assertNotNull(createDate);
        assertEquals("createDate", createDate.getField().getName());
        
        TypedField.DateTimeField modifyDate = table.modifyDate();
        assertNotNull(modifyDate);
        assertEquals("modifyDate", modifyDate.getField().getName());
    }

    @Test
    public void testBaseEntityFields() {
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 测试继承自 BaseEntity 的字段
        TypedField.StringField createUserId = table.createUserId();
        assertNotNull(createUserId);
        assertEquals("createUserId", createUserId.getField().getName());
        
        TypedField.StringField creator = table.creator();
        assertNotNull(creator);
        assertEquals("creator", creator.getField().getName());
        
        TypedField.StringField modifyUserId = table.modifyUserId();
        assertNotNull(modifyUserId);
        assertEquals("modifyUserId", modifyUserId.getField().getName());
        
        TypedField.StringField modifier = table.modifier();
        assertNotNull(modifier);
        assertEquals("modifier", modifier.getField().getName());
        
        TypedField.StringField belongId = table.belongId();
        assertNotNull(belongId);
        assertEquals("belongId", belongId.getField().getName());
    }

    @Test
    public void testFieldOperations() {
        TypeTableInfo table = TypeTableInfo.TABLE;
        
        // 测试字符串字段操作
        TypedField.StringField nameField = table.name();
        assertNotNull(nameField.like("%test%"));
        assertNotNull(nameField.eq("test"));
        assertNotNull(nameField.isNotNull());
        
        // 测试布尔字段操作
        TypedField.BooleanField hasAuditField = table.hasAudit();
        assertNotNull(hasAuditField.eq(true));
        assertNotNull(hasAuditField.isNull());
        
        // 测试日期时间字段操作
        TypedField.DateTimeField createDateField = table.createDate();
        assertNotNull(createDateField.isNotNull());
        // 注意：这里不能测试具体的日期比较，因为需要实际的 LocalDateTime 对象
    }
}
