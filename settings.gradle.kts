rootProject.name = "fast-web"
// 包含子模块
include("fast-client")
include("fast-server:core")
include("fast-server:auth")
include("fast-server:basic-api")
include("fast-server:tools")
include("fast-server:orm")
include("fast-server:orm-annotation-processor")


// 插件管理（统一版本）
pluginManagement {
  repositories {
    maven {
      isAllowInsecureProtocol = true
      url = uri("http://192.168.1.128/nexus/repository/maven-public/")
    }
  }
  plugins {
    id("com.github.johnrengelman.shadow") version "8.1.1"
  }
}

// 依赖仓库统一配置
dependencyResolutionManagement {
  repositories {
    maven {
      isAllowInsecureProtocol = true
      url = uri("http://192.168.1.128/nexus/repository/maven-public/")
    }
  }
}

include("fast-server:auth")
include("fast-server:basic-api")

include("fast-server:utils")

include("fast-server:orm-annotation-processor")
